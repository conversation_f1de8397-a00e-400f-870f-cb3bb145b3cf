# 玩家周期记录系统实现文档

## 概述

本文档描述了基于 Redis Set 的玩家周期记录系统的实现，该系统用于跟踪玩家在活动周期中的参与情况。

## 系统设计

### 数据结构

使用 Redis Set 存储玩家周期记录：
- **键名格式**: `activity_players:{activity_id}:{cycle_id}`
- **成员（member）**: 玩家ID（player_id）字符串
- **特性**: Set 自动去重，无序存储

### 核心功能

1. **写入时机**: 在活动事件处理成功后（HandleEvent 函数执行成功时）
2. **移除时机**: 在玩家成功领取奖励后（ClaimReward 函数执行成功时）
3. **TTL 策略**: 与活动周期数据相同的TTL策略（当前周期剩余天数 + 周期天数 + 1 + 随机1天）

## 实现细节

### 1. 配置层 (config/const.go)

添加了新的 Redis 键名常量：

```go
// 活动周期玩家记录Key (Set): activity_players:{activity_id}:{cycle_id}
RedisKeyActivityPlayers = "activity_players:%d:%d"

// 活动周期玩家记录Key
func ActivityPlayersKey(activityId int64, cycleId int32) string {
    return fmt.Sprintf(RedisKeyActivityPlayers, activityId, cycleId)
}
```

### 2. DAO 层 (internal/dao/dao_activity/player_cycle_dao.go)

实现了以下核心方法：

#### AddPlayerToCycle
- 使用 `SAdd` 命令将玩家ID添加到 Set 中
- Set 自动去重，重复添加不会产生副作用
- 包含完整的错误处理和日志记录

#### RemovePlayerFromCycle
- 使用 `SRem` 命令从 Set 中移除玩家
- 在玩家成功领取奖励后调用

#### SetPlayerCycleTTL
- 为 Set 设置过期时间
- 使用与活动周期数据相同的 TTL 策略

#### GetPlayersInCycle
- 获取活动周期内的玩家列表（无序）
- 使用 `SMembers` 命令获取所有成员
- 主要用于调试和统计

#### GetPlayerCycleCount
- 获取活动周期内的玩家总数
- 使用 `SCard` 命令

### 3. 业务逻辑层集成 (internal/logic/logic_activity/logic_activity.go)

#### 在 processActivityEvent 函数中
```go
// 6. 添加玩家到周期记录（成功处理事件后）
err = dao_activity.AddPlayerToCycle(ctx, activityCfg.Id, currentCycle.CycleId, playerId)
if err != nil {
    // 记录错误但不影响主流程，因为用户数据已经成功保存
    logrus.Errorf("failed to add player to cycle record: activityId=%d, cycleId=%d, playerId=%d, err=%v", 
        activityCfg.Id, currentCycle.CycleId, playerId, err)
}

// 7. 设置周期记录的TTL（如果需要）
err = dao_activity.SetPlayerCycleTTL(ctx, activityCfg.Id, currentCycle.CycleId, currentCycle)
if err != nil {
    // 记录错误但不影响主流程
    logrus.Errorf("failed to set TTL for player cycle record: activityId=%d, cycleId=%d, err=%v", 
        activityCfg.Id, currentCycle.CycleId, err)
}
```

#### 在 ClaimReward 函数中
```go
// 成功领取奖励后，从周期记录中移除玩家
err = dao_activity.RemovePlayerFromCycle(ctx, activityId, req.GetCycleId(), playerId)
if err != nil {
    // 记录错误但不影响主流程，因为奖励已经成功发放
    entry.Errorf("failed to remove player from cycle record: activityId=%d, cycleId=%d, playerId=%d, err=%v", 
        activityId, req.GetCycleId(), playerId, err)
}
```

## 错误处理和并发安全性

### 错误处理策略
- 所有 Redis 操作都有适当的错误处理
- 玩家周期记录操作失败不会影响主业务流程
- 详细的错误日志记录，便于问题排查

### 并发安全性
- 利用现有的分布式锁机制确保并发安全
- 使用 Redis 的原子操作保证数据一致性
- Set 的自动去重特性确保数据一致性

## 测试覆盖

实现了完整的单元测试 (test/player_cycle_test.go)：

### 基本功能测试
- 添加玩家到周期记录
- 获取周期内玩家数量
- 获取周期内玩家列表
- 设置TTL
- 移除玩家

### 边界情况测试
- 空周期操作
- 大玩家ID操作
- 重复添加玩家
- 移除不存在的玩家

### 键名生成测试
- 验证键名格式正确性

## 性能考虑

### Redis Set 优势
- O(1) 的添加、删除和查询操作
- O(1) 的计数操作
- 自动去重，简化业务逻辑
- 内存效率高，无需存储分数

### TTL 策略
- 自动清理过期数据，避免内存泄漏
- 与活动周期数据保持一致的生命周期

## 使用示例

```go
// 添加玩家到周期记录
err := dao_activity.AddPlayerToCycle(ctx, activityId, cycleId, playerId)

// 获取周期内玩家数量
count, err := dao_activity.GetPlayerCycleCount(ctx, activityId, cycleId)

// 移除玩家
err := dao_activity.RemovePlayerFromCycle(ctx, activityId, cycleId, playerId)

// 设置TTL
err := dao_activity.SetPlayerCycleTTL(ctx, activityId, cycleId, cycle)
```

## 监控和运维

### 日志记录
- 所有关键操作都有详细的日志记录
- 错误日志包含完整的上下文信息
- 支持调试级别的日志输出

### Redis 键监控
- 键名格式: `activity_players:{activity_id}:{cycle_id}`
- 可通过 `SCARD` 命令监控 Set 大小
- 可通过 `SMEMBERS` 命令查看所有成员
- TTL 自动管理，无需手动清理

## 总结

该实现提供了一个高效、可靠的玩家周期记录系统，具有以下特点：

1. **高性能**: 基于 Redis Set 的 O(1) 操作复杂度
2. **数据一致性**: 使用原子操作和分布式锁保证一致性，Set 自动去重
3. **容错性**: 完善的错误处理，不影响主业务流程
4. **可维护性**: 清晰的代码结构和完整的测试覆盖
5. **可扩展性**: 模块化设计，易于扩展新功能
6. **简洁性**: Set 结构简单，无需管理时间戳，降低复杂度

该系统已通过完整的单元测试验证，可以安全地部署到生产环境中使用。
