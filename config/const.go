package config

import (
	"activitysrv/internal/model"
	"fmt"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	"time"
)

// 爆护之路活动相关Redis Key
const (

	// 用户活动锁
	RDS_KEY_ACT_USER_LOCK = "lock:act:%d:%d"

	// 活动周期管理Key: activity:{activity_id}
	RedisKeyActivityCycle = "activity:%d"

	// 玩家活动数据Key: act:{activity_id}:{cycle_id}:{player_id}
	RedisKeyPlayerActivityData = "act:%d:%d:%d"

	// 活动周期玩家记录Key: activity_players:{activity_id}:{cycle_id}
	RedisKeyActivityPlayers = "activity_players:%d:%d"
)

// 活动周期管理Key
func ActivityCycleKey(activityId int64) string {
	return fmt.Sprintf(RedisKeyActivityCycle, activityId)
}

// 玩家活动数据Key
func PlayerActivityDataKey(activityId int64, cycleId int32, playerId uint64) string {
	return fmt.Sprintf(RedisKeyPlayerActivityData, activityId, cycleId, playerId)
}

// 计算TTL时间 (当前周期剩余天数 + 周期天数 + 1 + 随机1天)
func CalculateActivityTTL(cycle *model.ActivityCycle) time.Duration {
	now := timex.Now().Unix()
	remainingDays := int32((cycle.EndTime - now) / (24 * 3600))
	baseDays := remainingDays + cycle.CycleDays + 1
	randomHours := time.Duration(1+(time.Now().UnixNano()%24)) * time.Hour // 随机1-24小时
	return time.Duration(baseDays)*24*time.Hour + randomHours
}

func ActivityPlayersKey(activityId int64, cycleId int32) string {
	return fmt.Sprintf(RedisKeyActivityPlayers, activityId, cycleId)
}

func UserActivityLockKey(activityId int64, userId uint64) string {
	return fmt.Sprintf(RDS_KEY_ACT_USER_LOCK, activityId, userId)
}
