package test

import (
	"activitysrv/config"
	"activitysrv/internal/dao/dao_activity"
	"activitysrv/internal/model"
	"context"
	"testing"
	"time"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_redis"
	"github.com/spf13/viper"
	"github.com/stretchr/testify/assert"
)

// InitTestEnv 初始化测试环境
func InitTestEnv() {
	// 设置Redis配置
	conf := map[string]string{
		"addr":   "************:6379",
		"passwd": "8888",
	}
	viper.SetDefault("redis_list", map[string]interface{}{
		dict_redis.RDBActivity: conf,
		dict_redis.RDBLock:     conf,
	})
}

// TestPlayerCycleOperations 测试玩家周期记录的基本操作（使用 Set）
func TestPlayerCycleOperations(t *testing.T) {
	// 初始化测试环境
	InitTestEnv()

	ctx := context.Background()
	activityId := int64(9001)  // 使用不同的ID避免冲突
	cycleId := int32(9)
	playerId1 := uint64(10001)
	playerId2 := uint64(10002)

	// 创建测试用的活动周期
	cycle := model.NewActivityCycle(cycleId, 7, time.Now().Unix(), time.Now().Unix()+7*24*3600)

	// 测试添加玩家到周期记录
	t.Run("AddPlayerToCycle", func(t *testing.T) {
		// 添加第一个玩家
		err := dao_activity.AddPlayerToCycle(ctx, activityId, cycleId, playerId1)
		assert.NoError(t, err, "添加玩家1到周期记录应该成功")

		// 添加第二个玩家
		err = dao_activity.AddPlayerToCycle(ctx, activityId, cycleId, playerId2)
		assert.NoError(t, err, "添加玩家2到周期记录应该成功")

		// 重复添加同一个玩家（Set 会自动去重）
		err = dao_activity.AddPlayerToCycle(ctx, activityId, cycleId, playerId1)
		assert.NoError(t, err, "重复添加玩家1应该成功")
	})

	// 测试获取周期内玩家数量
	t.Run("GetPlayerCycleCount", func(t *testing.T) {
		count, err := dao_activity.GetPlayerCycleCount(ctx, activityId, cycleId)
		assert.NoError(t, err, "获取玩家数量应该成功")
		assert.Equal(t, int64(2), count, "周期内应该有2个玩家（Set自动去重）")
	})

	// 测试获取周期内玩家列表
	t.Run("GetPlayersInCycle", func(t *testing.T) {
		players, err := dao_activity.GetPlayersInCycle(ctx, activityId, cycleId, 10)
		assert.NoError(t, err, "获取玩家列表应该成功")
		assert.Len(t, players, 2, "应该返回2个玩家")
		
		// 验证玩家ID是否正确（Set 无序，所以用 map 检查）
		playerMap := make(map[uint64]bool)
		for _, pid := range players {
			playerMap[pid] = true
		}
		assert.True(t, playerMap[playerId1], "应该包含玩家1")
		assert.True(t, playerMap[playerId2], "应该包含玩家2")
	})

	// 测试设置TTL
	t.Run("SetPlayerCycleTTL", func(t *testing.T) {
		err := dao_activity.SetPlayerCycleTTL(ctx, activityId, cycleId, cycle)
		assert.NoError(t, err, "设置TTL应该成功")
	})

	// 测试移除玩家
	t.Run("RemovePlayerFromCycle", func(t *testing.T) {
		// 移除玩家1
		err := dao_activity.RemovePlayerFromCycle(ctx, activityId, cycleId, playerId1)
		assert.NoError(t, err, "移除玩家1应该成功")

		// 验证玩家数量减少
		count, err := dao_activity.GetPlayerCycleCount(ctx, activityId, cycleId)
		assert.NoError(t, err, "获取玩家数量应该成功")
		assert.Equal(t, int64(1), count, "移除后周期内应该有1个玩家")

		// 移除不存在的玩家（应该成功但不影响结果）
		err = dao_activity.RemovePlayerFromCycle(ctx, activityId, cycleId, uint64(99999))
		assert.NoError(t, err, "移除不存在的玩家应该成功")

		// 验证玩家数量不变
		count, err = dao_activity.GetPlayerCycleCount(ctx, activityId, cycleId)
		assert.NoError(t, err, "获取玩家数量应该成功")
		assert.Equal(t, int64(1), count, "移除不存在玩家后数量应该不变")
	})

	// 清理测试数据
	t.Cleanup(func() {
		key := config.ActivityPlayersKey(activityId, cycleId)
		// 这里可以添加清理Redis数据的代码
		_ = key
	})
}

// TestPlayerCycleKeyGeneration 测试键名生成
func TestPlayerCycleKeyGeneration(t *testing.T) {
	activityId := int64(1001)
	cycleId := int32(5)
	
	expectedKey := "activity_players:1001:5"
	actualKey := config.ActivityPlayersKey(activityId, cycleId)
	
	assert.Equal(t, expectedKey, actualKey, "生成的键名应该符合预期格式")
}

// TestPlayerCycleEdgeCases 测试边界情况
func TestPlayerCycleEdgeCases(t *testing.T) {
	// 初始化测试环境
	InitTestEnv()

	ctx := context.Background()
	activityId := int64(9002)  // 使用不同的ID避免冲突
	cycleId := int32(9)
	playerId := uint64(20001)

	t.Run("EmptyCycleOperations", func(t *testing.T) {
		// 测试空周期的操作
		count, err := dao_activity.GetPlayerCycleCount(ctx, activityId, cycleId)
		assert.NoError(t, err, "获取空周期玩家数量应该成功")
		assert.Equal(t, int64(0), count, "空周期应该有0个玩家")

		players, err := dao_activity.GetPlayersInCycle(ctx, activityId, cycleId, 10)
		assert.NoError(t, err, "获取空周期玩家列表应该成功")
		assert.Len(t, players, 0, "空周期应该返回空列表")

		// 从空周期移除玩家
		err = dao_activity.RemovePlayerFromCycle(ctx, activityId, cycleId, playerId)
		assert.NoError(t, err, "从空周期移除玩家应该成功")
	})

	t.Run("LargePlayerIdOperations", func(t *testing.T) {
		// 测试大的玩家ID
		largePlayerId := uint64(18446744073709551615) // uint64最大值
		
		err := dao_activity.AddPlayerToCycle(ctx, activityId, cycleId, largePlayerId)
		assert.NoError(t, err, "添加大玩家ID应该成功")

		count, err := dao_activity.GetPlayerCycleCount(ctx, activityId, cycleId)
		assert.NoError(t, err, "获取玩家数量应该成功")
		assert.Equal(t, int64(1), count, "应该有1个玩家")

		err = dao_activity.RemovePlayerFromCycle(ctx, activityId, cycleId, largePlayerId)
		assert.NoError(t, err, "移除大玩家ID应该成功")
	})

	t.Run("SetDeduplication", func(t *testing.T) {
		// 测试 Set 的去重特性
		testPlayerId := uint64(30001)
		
		// 多次添加同一个玩家
		for i := 0; i < 5; i++ {
			err := dao_activity.AddPlayerToCycle(ctx, activityId, cycleId, testPlayerId)
			assert.NoError(t, err, "添加玩家应该成功")
		}

		// 验证只有一个玩家
		count, err := dao_activity.GetPlayerCycleCount(ctx, activityId, cycleId)
		assert.NoError(t, err, "获取玩家数量应该成功")
		assert.Equal(t, int64(1), count, "Set应该自动去重，只有1个玩家")

		// 清理
		err = dao_activity.RemovePlayerFromCycle(ctx, activityId, cycleId, testPlayerId)
		assert.NoError(t, err, "清理玩家应该成功")
	})
}
