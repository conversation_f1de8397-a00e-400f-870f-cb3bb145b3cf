package dao_activity

import (
	"activitysrv/config"
	"activitysrv/internal/model"
	"context"
	"fmt"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"strconv"
)

// AddPlayerToCycle 添加玩家到活动周期记录
func AddPlayerToCycle(ctx context.Context, activityId int64, cycleId int32, playerId uint64) error {
	entry := logx.NewLogEntry(ctx)

	key := config.ActivityPlayersKey(activityId, cycleId)
	member := strconv.FormatUint(playerId, 10)

	// 使用 SADD 添加玩家到 Set
	_, err := redisx.GetActivityCli().SAdd(ctx, key, member).Result()

	if err != nil {
		entry.Errorf("failed to add player to cycle: activityId=%d, cycleId=%d, playerId=%d, err=%v",
			activityId, cycleId, playerId, err)
		return fmt.Errorf("failed to add player to cycle: %w", err)
	}

	return nil
}

// RemovePlayerFromCycle 从活动周期记录中移除玩家
// 在玩家成功领取奖励后调用
func RemovePlayerFromCycle(ctx context.Context, activityId int64, cycleId int32, playerId uint64) error {
	entry := logx.NewLogEntry(ctx)

	key := config.ActivityPlayersKey(activityId, cycleId)
	member := strconv.FormatUint(playerId, 10)

	_, err := redisx.GetActivityCli().SRem(ctx, key, member).Result()
	if err != nil {
		entry.Errorf("failed to remove player from cycle: activityId=%d, cycleId=%d, playerId=%d, err=%v",
			activityId, cycleId, playerId, err)
		return fmt.Errorf("failed to remove player from cycle: %w", err)
	}

	return nil
}

// SetPlayerCycleTTL 为活动周期玩家记录设置TTL
// 使用与活动周期数据相同的TTL策略
func SetPlayerCycleTTL(ctx context.Context, activityId int64, cycleId int32, cycle *model.ActivityCycle) error {
	entry := logx.NewLogEntry(ctx)
	key := config.ActivityPlayersKey(activityId, cycleId)
	redisCli := redisx.GetActivityCli()

	ttl := config.CalculateActivityTTL(cycle)

	err := redisCli.Expire(ctx, key, ttl).Err()
	if err != nil {
		entry.Errorf("failed to set TTL for player cycle: activityId=%d, cycleId=%d, ttl=%v, err=%v",
			activityId, cycleId, ttl, err)
		return fmt.Errorf("failed to set TTL for player cycle: %w", err)
	}

	entry.Debugf("TTL set for player cycle: activityId=%d, cycleId=%d, ttl=%v",
		activityId, cycleId, ttl)

	return nil
}

// GetPlayersInCycle 获取活动周期内的玩家列表
// 可选方法，主要用于调试和统计
func GetPlayersInCycle(ctx context.Context, activityId int64, cycleId int32, limit int64) ([]uint64, error) {
	entry := logx.NewLogEntry(ctx)
	key := config.ActivityPlayersKey(activityId, cycleId)
	redisCli := redisx.GetActivityCli()

	// 获取 Set 中的所有成员
	members, err := redisCli.SMembers(ctx, key).Result()
	if err != nil {
		entry.Errorf("failed to get players in cycle: activityId=%d, cycleId=%d, err=%v",
			activityId, cycleId, err)
		return nil, fmt.Errorf("failed to get players in cycle: %w", err)
	}

	// 如果需要限制数量，截取前 limit 个
	if limit > 0 && int64(len(members)) > limit {
		members = members[:limit]
	}

	playerIds := make([]uint64, 0, len(members))
	for _, member := range members {
		if playerId, err := strconv.ParseUint(member, 10, 64); err == nil {
			playerIds = append(playerIds, playerId)
		} else {
			entry.Warnf("invalid player ID in cycle: %s", member)
		}
	}

	entry.Debugf("found %d players in cycle: activityId=%d, cycleId=%d",
		len(playerIds), activityId, cycleId)

	return playerIds, nil
}

// GetPlayerCycleCount 获取活动周期内的玩家总数
func GetPlayerCycleCount(ctx context.Context, activityId int64, cycleId int32) (int64, error) {
	entry := logx.NewLogEntry(ctx)
	key := config.ActivityPlayersKey(activityId, cycleId)
	redisCli := redisx.GetActivityCli()

	count, err := redisCli.SCard(ctx, key).Result()
	if err != nil {
		entry.Errorf("failed to get player count in cycle: activityId=%d, cycleId=%d, err=%v",
			activityId, cycleId, err)
		return 0, fmt.Errorf("failed to get player count in cycle: %w", err)
	}

	entry.Debugf("player count in cycle: activityId=%d, cycleId=%d, count=%d",
		activityId, cycleId, count)

	return count, nil
}
