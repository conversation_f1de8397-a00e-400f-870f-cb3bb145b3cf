package dao_activity

import (
	"activitysrv/config"
	"activitysrv/internal/model"
	"context"
	"fmt"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"github.com/go-redis/redis/v8"
	"strconv"
	"time"
)

// AddPlayerToCycle 添加玩家到活动周期记录
func AddPlayerToCycle(ctx context.Context, activityId int64, cycleId int32, playerId uint64) {
	entry := logx.NewLogEntry(ctx)
	key := config.ActivityPlayersKey(activityId, cycleId)
	redisCli := redisx.GetActivityCli()

	// 使用当前时间戳作为分数
	score := float64(time.Now().Unix())
	member := strconv.FormatUint(playerId, 10)

	// 使用 ZADD NX 选项，只在成员不存在时添加
	_, err := redisCli.ZAddArgs(ctx, key, redis.ZAddArgs{
		NX:      true, // 只在成员不存在时添加
		Members: []redis.Z{{Score: score, Member: member}},
	}).Result()

	if err != nil {
		entry.Errorf("failed to add player to cycle: activityId=%d, cycleId=%d, playerId=%d, err=%v",
			activityId, cycleId, playerId, err)
	}
	return
}

// RemovePlayerFromCycle 从活动周期记录中移除玩家
// 在玩家成功领取奖励后调用
func RemovePlayerFromCycle(ctx context.Context, activityId int64, cycleId int32, playerId uint64) {
	entry := logx.NewLogEntry(ctx)
	key := config.ActivityPlayersKey(activityId, cycleId)
	redisCli := redisx.GetActivityCli()

	member := strconv.FormatUint(playerId, 10)

	_, err := redisCli.ZRem(ctx, key, member).Result()
	if err != nil {
		entry.Errorf("failed to remove player from cycle: activityId=%d, cycleId=%d, playerId=%d, err=%v",
			activityId, cycleId, playerId, err)
	}

	return
}

// SetPlayerCycleTTL 为活动周期玩家记录设置TTL
// 使用与活动周期数据相同的TTL策略
func SetPlayerCycleTTL(ctx context.Context, activityId int64, cycleId int32, cycle *model.ActivityCycle) error {
	entry := logx.NewLogEntry(ctx)
	key := config.ActivityPlayersKey(activityId, cycleId)
	redisCli := redisx.GetActivityCli()

	ttl := config.CalculateActivityTTL(cycle)

	err := redisCli.Expire(ctx, key, ttl).Err()
	if err != nil {
		entry.Errorf("failed to set TTL for player cycle: activityId=%d, cycleId=%d, ttl=%v, err=%v",
			activityId, cycleId, ttl, err)
		return fmt.Errorf("failed to set TTL for player cycle: %w", err)
	}

	entry.Debugf("TTL set for player cycle: activityId=%d, cycleId=%d, ttl=%v",
		activityId, cycleId, ttl)

	return nil
}

// GetPlayersInCycle 获取活动周期内的玩家列表（按参与时间排序）
// 可选方法，主要用于调试和统计
func GetPlayersInCycle(ctx context.Context, activityId int64, cycleId int32, limit int64) ([]uint64, error) {
	entry := logx.NewLogEntry(ctx)
	key := config.ActivityPlayersKey(activityId, cycleId)
	redisCli := redisx.GetActivityCli()

	// 获取按分数排序的成员列表（最早参与的在前）
	members, err := redisCli.ZRange(ctx, key, 0, limit-1).Result()
	if err != nil {
		entry.Errorf("failed to get players in cycle: activityId=%d, cycleId=%d, err=%v",
			activityId, cycleId, err)
		return nil, fmt.Errorf("failed to get players in cycle: %w", err)
	}

	playerIds := make([]uint64, 0, len(members))
	for _, member := range members {
		if playerId, err := strconv.ParseUint(member, 10, 64); err == nil {
			playerIds = append(playerIds, playerId)
		} else {
			entry.Warnf("invalid player ID in cycle: %s", member)
		}
	}

	entry.Debugf("found %d players in cycle: activityId=%d, cycleId=%d",
		len(playerIds), activityId, cycleId)

	return playerIds, nil
}

// GetPlayerCycleCount 获取活动周期内的玩家总数
func GetPlayerCycleCount(ctx context.Context, activityId int64, cycleId int32) (int64, error) {
	entry := logx.NewLogEntry(ctx)
	key := config.ActivityPlayersKey(activityId, cycleId)
	redisCli := redisx.GetActivityCli()

	count, err := redisCli.ZCard(ctx, key).Result()
	if err != nil {
		entry.Errorf("failed to get player count in cycle: activityId=%d, cycleId=%d, err=%v",
			activityId, cycleId, err)
		return 0, fmt.Errorf("failed to get player count in cycle: %w", err)
	}

	entry.Debugf("player count in cycle: activityId=%d, cycleId=%d, count=%d",
		activityId, cycleId, count)

	return count, nil
}
